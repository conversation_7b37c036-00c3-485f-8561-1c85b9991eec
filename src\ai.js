'use strict';

/**
 * AI Auto-Complete System
 *
 * Features:
 * 1. Math Auto-Complete:
 *    - Standard: +, -, *, /
 *    - Mobile: ×, ÷, − (Unicode minus)
 *    - Functions: sqrt, sin, cos, tan, log, ln, abs, round, floor, ceil, pow
 *    - Examples: 5 × 3 = → shows "15", sqrt(25) = → shows "5"
 *
 * 2. Word Auto-Complete:
 *    - Common English words
 *    - Programming keywords
 *    - Context-aware suggestions
 *    - Smart capitalization
 *    - Examples: "hel" → suggests "hello", "func" → suggests "function"
 *
 * Usage:
 * - Math: Type expression ending with =
 * - Words: Type partial word (3+ characters)
 * - Accept: Click ghost, press Tab, or press Enter
 * - Dismiss: Press Esc or click elsewhere
 */

class BaseAutoComplete {
    constructor() {
        this.ghostElement = null;
        this.currentSuggestion = null;
        this.isActive = false;
        this.suggestionType = null; // 'math' or 'word'
        this.init();
    }

    init() {
        this.createGhostElement();
        this.bindEvents();
    }

    createGhostElement() {
        this.ghostElement = document.createElement('div');
        this.ghostElement.className = 'math-ghost-suggestion';
        this.ghostElement.style.cssText = `
            position: absolute;
            color: rgba(255, 255, 255, 0.4);
            pointer-events: auto;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            white-space: pre;
            z-index: 1000;
            display: none;
            user-select: none;
            background: rgba(0, 180, 216, 0.1);
            border-radius: 3px;
            padding: 0 2px;
            transition: all 0.2s ease;
        `;
        
        this.ghostElement.addEventListener('click', () => {
            this.acceptSuggestion();
        });

        this.ghostElement.addEventListener('mouseenter', () => {
            if (this.suggestionType === 'word') {
                this.ghostElement.style.background = 'rgba(76, 175, 80, 0.2)';
            } else {
                this.ghostElement.style.background = 'rgba(0, 180, 216, 0.2)';
            }
        });

        this.ghostElement.addEventListener('mouseleave', () => {
            if (this.suggestionType === 'word') {
                this.ghostElement.style.background = 'rgba(76, 175, 80, 0.1)';
            } else {
                this.ghostElement.style.background = 'rgba(0, 180, 216, 0.1)';
            }
        });

        document.body.appendChild(this.ghostElement);
    }

    bindEvents() {
        const editor = document.getElementById('editor');
        if (!editor) return;

        editor.addEventListener('input', (e) => {
            this.handleInput(e);
        });

        editor.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        }, true); // Use capture phase to handle before other listeners

        editor.addEventListener('scroll', () => {
            this.hideGhost();
        });

        // Hide ghost when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (e.target !== this.ghostElement && e.target !== editor) {
                this.hideGhost();
            }
        });
    }

    handleInput(event) {
        const editor = event.target;
        const text = editor.value;
        const cursorPos = editor.selectionStart;

        // Get current line
        const lines = text.substring(0, cursorPos).split('\n');
        const currentLine = lines[lines.length - 1];

        // Try math auto-complete first, then word auto-complete
        if (!this.checkForMathExpression(currentLine, editor, cursorPos)) {
            this.checkForWordCompletion(currentLine, editor, cursorPos, text);
        }
    }

    handleKeydown(event) {
        if (this.isActive) {
            if (event.key === 'Tab' || event.key === 'ArrowRight') {
                event.preventDefault();
                event.stopPropagation();
                this.acceptSuggestion();
            } else if (event.key === 'Enter') {
                event.preventDefault();
                event.stopPropagation();
                this.acceptSuggestion();
                // Add a new line after accepting the suggestion
                const editor = document.getElementById('editor');
                if (editor) {
                    const cursorPos = editor.selectionStart;
                    const text = editor.value;
                    const newText = text.substring(0, cursorPos) + '\n' + text.substring(cursorPos);
                    editor.value = newText;
                    editor.setSelectionRange(cursorPos + 1, cursorPos + 1);
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }
            } else if (event.key === 'Escape') {
                this.hideGhost();
            }
        }
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced math expression patterns - support complex expressions
        const patterns = [
            // Complex expressions with equals: 1+2+3=, 5*2-3+1=, (2+3)*4-1=
            /^(.+?)\s*=\s*$/,
            // Preview mode - show result for expressions without equals
            // Support multiple operators: 1+2+3, 5*2-3, (2+3)*4
            /^([0-9+\-*/()×÷−.\s]+)$/
        ];

        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                let expression;
                if (pattern.source.includes('=')) {
                    // Has equals sign - extract expression before =
                    expression = match[1].trim();
                } else {
                    // No equals sign - use full match for preview
                    expression = match[0].trim();
                }

                // Only process if expression contains operators and numbers
                if (this.isValidMathExpression(expression)) {
                    const result = this.evaluateExpression(expression);

                    if (result !== null) {
                        // Check if line ends with = to determine suggestion format
                        const suggestion = line.includes('=') ? result : `= ${result}`;
                        this.showGhost(suggestion, editor, cursorPos, 'math');
                        return true;
                    }
                }
            }
        }

        return false;
    }

    checkForWordCompletion(line, editor, cursorPos, fullText) {
        // Get the word being typed at cursor position
        const wordMatch = this.getCurrentWord(line, cursorPos, fullText);
        if (!wordMatch) {
            this.hideGhost();
            return false;
        }

        const { word } = wordMatch;

        // Only suggest for words with 3+ characters
        if (word.length < 3) {
            this.hideGhost();
            return false;
        }

        // Find best word suggestion
        const suggestion = this.findBestWordSuggestion(word, fullText);
        if (suggestion && suggestion.toLowerCase() !== word.toLowerCase()) {
            // Show only the remaining part of the word
            const completion = suggestion.substring(word.length);
            this.showGhost(completion, editor, cursorPos, 'word');
            return true;
        }

        this.hideGhost();
        return false;
    }

    getCurrentWord(line, cursorPos, fullText) {
        // Find word boundaries around cursor
        const textBeforeCursor = fullText.substring(0, cursorPos);
        const lines = textBeforeCursor.split('\n');
        const currentLineText = lines[lines.length - 1];

        // Find the start of the current word by looking backwards from cursor
        let wordStart = currentLineText.length;
        for (let i = currentLineText.length - 1; i >= 0; i--) {
            const char = currentLineText[i];
            if (/[a-zA-Z0-9_]/.test(char)) {
                wordStart = i;
            } else {
                wordStart = i + 1; // Start after the non-word character
                break;
            }
        }

        // Extract the current word
        const word = currentLineText.substring(wordStart);

        // Only return valid words that start with a letter
        if (word && /^[a-zA-Z][a-zA-Z0-9_]*$/.test(word)) {
            return { word, startPos: wordStart };
        }

        return null;
    }

    findBestWordSuggestion(partialWord, context) {
        const lowerPartial = partialWord.toLowerCase();

        // Get word dictionary
        const dictionary = this.getWordDictionary(context);

        // Find exact prefix matches first
        const exactMatches = dictionary.filter(word =>
            word.toLowerCase().startsWith(lowerPartial) &&
            word.toLowerCase() !== lowerPartial
        );

        if (exactMatches.length > 0) {
            // Sort by length (prefer shorter completions) and frequency
            exactMatches.sort((a, b) => {
                const aScore = this.getWordScore(a, context);
                const bScore = this.getWordScore(b, context);
                if (aScore !== bScore) return bScore - aScore;
                return a.length - b.length;
            });

            // Preserve original capitalization pattern
            return this.preserveCapitalization(partialWord, exactMatches[0]);
        }

        return null;
    }

    getWordDictionary(context) {
        // Comprehensive word dictionary with 5000+ words from various domains
        const commonWords = [
            // Most common English words (top 1000)
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use',
            'about', 'after', 'again', 'before', 'being', 'below', 'between', 'both', 'during', 'each', 'few', 'from', 'further', 'here', 'into', 'more', 'most', 'other', 'over', 'same', 'some', 'such', 'than', 'that', 'their', 'them', 'these', 'they', 'this', 'those', 'through', 'time', 'very', 'what', 'when', 'where', 'which', 'while', 'with', 'would', 'your',
            'hello', 'world', 'example', 'consider', 'minute', 'accord', 'evident', 'practice', 'intend', 'concern', 'commit', 'issue', 'approach', 'establish', 'conduct', 'engage', 'obtain', 'scarce', 'policy', 'straight', 'stock', 'apparent', 'property', 'fancy', 'concept', 'court', 'appoint', 'passage', 'instance', 'coast', 'project', 'commission', 'constant', 'circumstances', 'constitute', 'level', 'affect', 'institute', 'render', 'appeal', 'generate', 'theory', 'range', 'campaign', 'league', 'labor', 'confer', 'grant', 'dwell', 'entertain', 'contract', 'earnest', 'yield', 'wander', 'insist', 'knight', 'convince', 'inspire', 'convention', 'skill', 'financial', 'reflect', 'novel', 'furnish', 'compel', 'venture', 'territory', 'temper', 'intimate', 'undertake', 'majority', 'assert', 'crew', 'chamber', 'humble', 'scheme', 'keen', 'liberal', 'despair', 'attitude', 'justify', 'flag',

            // Programming languages and technologies
            'javascript', 'typescript', 'python', 'java', 'csharp', 'cplusplus', 'golang', 'rust', 'swift', 'kotlin', 'scala', 'ruby', 'php', 'perl', 'haskell', 'clojure', 'erlang', 'elixir', 'dart', 'lua', 'assembly', 'fortran', 'cobol', 'pascal', 'delphi', 'visualbasic', 'matlab', 'octave', 'mathematica', 'stata',
            'html', 'css', 'sass', 'scss', 'less', 'stylus', 'postcss', 'tailwind', 'bootstrap', 'bulma', 'foundation', 'materialize', 'semantic', 'chakra', 'antdesign', 'material',
            'react', 'angular', 'vue', 'svelte', 'ember', 'backbone', 'knockout', 'polymer', 'lit', 'stencil', 'alpine', 'stimulus', 'htmx', 'jquery', 'zepto', 'mootools', 'prototype', 'dojo', 'extjs', 'sencha',
            'node', 'express', 'fastify', 'koa', 'hapi', 'nestjs', 'nextjs', 'nuxtjs', 'gatsby', 'gridsome', 'sapper', 'sveltekit', 'remix', 'astro', 'eleventy', 'jekyll', 'hugo', 'hexo', 'vuepress', 'docusaurus',

            // Database and storage
            'mysql', 'postgresql', 'sqlite', 'mongodb', 'redis', 'elasticsearch', 'solr', 'cassandra', 'dynamodb', 'firestore', 'couchdb', 'neo4j', 'influxdb', 'timescaledb', 'clickhouse', 'snowflake', 'bigquery', 'redshift', 'athena', 'presto', 'spark', 'hadoop', 'kafka', 'rabbitmq', 'activemq', 'zeromq', 'nats', 'pulsar',

            // Cloud and DevOps
            'docker', 'kubernetes', 'podman', 'containerd', 'terraform', 'ansible', 'puppet', 'chef', 'saltstack', 'vagrant', 'packer', 'consul', 'vault', 'nomad', 'jenkins', 'gitlab', 'github', 'bitbucket', 'azure', 'amazon', 'google', 'digitalocean', 'linode', 'vultr', 'heroku', 'netlify', 'vercel', 'cloudflare', 'fastly',

            // Programming concepts and patterns
            'algorithm', 'datastructure', 'linkedlist', 'hashtable', 'binarytree', 'graph', 'stack', 'queue', 'heap', 'trie', 'sorting', 'searching', 'recursion', 'iteration', 'dynamic', 'greedy', 'backtracking', 'memoization', 'optimization', 'complexity', 'bignotation', 'asymptotic', 'polynomial', 'exponential', 'logarithmic', 'constant', 'linear', 'quadratic', 'cubic',
            'singleton', 'factory', 'builder', 'prototype', 'adapter', 'decorator', 'facade', 'proxy', 'observer', 'strategy', 'command', 'state', 'template', 'visitor', 'iterator', 'composite', 'bridge', 'flyweight', 'mediator', 'memento', 'chainofresponsibility',

            // Software development methodologies
            'agile', 'scrum', 'kanban', 'waterfall', 'devops', 'cicd', 'continuous', 'integration', 'deployment', 'delivery', 'testing', 'unittest', 'integration', 'acceptance', 'performance', 'load', 'stress', 'security', 'penetration', 'vulnerability', 'automation', 'pipeline', 'workflow', 'orchestration', 'monitoring', 'logging', 'metrics', 'alerting', 'observability', 'tracing',

            // Web development terms
            'frontend', 'backend', 'fullstack', 'responsive', 'mobile', 'progressive', 'webapp', 'spa', 'ssr', 'ssg', 'jamstack', 'headless', 'microservices', 'monolith', 'serverless', 'lambda', 'function', 'edge', 'cdn', 'cache', 'optimization', 'performance', 'accessibility', 'seo', 'analytics', 'tracking', 'conversion', 'funnel', 'retention', 'engagement',

            // Data science and AI
            'machine', 'learning', 'artificial', 'intelligence', 'neural', 'network', 'deep', 'supervised', 'unsupervised', 'reinforcement', 'classification', 'regression', 'clustering', 'dimensionality', 'reduction', 'feature', 'engineering', 'selection', 'extraction', 'preprocessing', 'normalization', 'standardization', 'encoding', 'scaling', 'imputation', 'outlier', 'detection', 'validation', 'crossvalidation', 'hyperparameter', 'tuning', 'optimization', 'gradient', 'descent', 'backpropagation', 'overfitting', 'underfitting', 'regularization', 'dropout', 'batchnorm', 'activation', 'sigmoid', 'relu', 'tanh', 'softmax', 'convolution', 'pooling', 'lstm', 'gru', 'transformer', 'attention', 'encoder', 'decoder', 'embedding', 'tokenization', 'preprocessing', 'postprocessing',

            // Business and finance terms
            'business', 'finance', 'accounting', 'revenue', 'profit', 'loss', 'margin', 'budget', 'forecast', 'projection', 'analysis', 'report', 'dashboard', 'metrics', 'kpi', 'roi', 'conversion', 'acquisition', 'retention', 'churn', 'lifetime', 'value', 'customer', 'segment', 'persona', 'journey', 'funnel', 'pipeline', 'sales', 'marketing', 'advertising', 'campaign', 'brand', 'identity', 'positioning', 'messaging', 'content', 'strategy', 'tactics', 'execution', 'measurement', 'optimization', 'testing', 'experiment', 'hypothesis', 'statistical', 'significance', 'confidence', 'interval', 'correlation', 'causation', 'regression', 'variance', 'standard', 'deviation', 'distribution', 'probability', 'bayesian', 'frequentist',

            // Science and technology
            'science', 'technology', 'research', 'development', 'innovation', 'discovery', 'invention', 'patent', 'intellectual', 'property', 'copyright', 'trademark', 'license', 'opensource', 'proprietary', 'commercial', 'academic', 'publication', 'journal', 'conference', 'symposium', 'workshop', 'seminar', 'presentation', 'demonstration', 'prototype', 'proof', 'concept', 'feasibility', 'study', 'experiment', 'hypothesis', 'theory', 'model', 'simulation', 'validation', 'verification', 'testing', 'quality', 'assurance', 'control', 'standard', 'specification', 'requirement', 'documentation', 'manual', 'guide', 'tutorial', 'reference', 'api', 'sdk', 'framework', 'library', 'package', 'module', 'component', 'service', 'microservice', 'architecture', 'design', 'pattern', 'principle', 'practice', 'methodology', 'process', 'workflow', 'pipeline', 'automation', 'orchestration', 'deployment', 'release', 'version', 'update', 'patch', 'hotfix', 'bugfix', 'feature', 'enhancement', 'improvement', 'optimization', 'refactoring', 'migration', 'upgrade', 'downgrade', 'rollback', 'recovery', 'backup', 'restore', 'synchronization', 'replication', 'clustering', 'scaling', 'horizontal', 'vertical', 'load', 'balancing', 'distribution', 'partitioning', 'sharding', 'indexing', 'caching', 'memoization', 'compression', 'encryption', 'decryption', 'hashing', 'signing', 'verification', 'authentication', 'authorization', 'permission', 'access', 'control', 'security', 'privacy', 'compliance', 'audit', 'logging', 'monitoring', 'alerting', 'notification', 'dashboard', 'visualization', 'reporting', 'analytics', 'intelligence', 'insight', 'recommendation', 'prediction', 'forecasting', 'trending', 'anomaly', 'detection', 'classification', 'clustering', 'segmentation', 'personalization', 'customization', 'configuration', 'settings', 'preferences', 'options', 'parameters', 'arguments', 'variables', 'constants', 'literals', 'expressions', 'statements', 'declarations', 'definitions', 'implementations', 'interfaces', 'abstractions', 'inheritance', 'polymorphism', 'encapsulation', 'composition', 'aggregation', 'association', 'dependency', 'injection', 'inversion', 'control', 'separation', 'concerns', 'single', 'responsibility', 'open', 'closed', 'liskov', 'substitution', 'interface', 'segregation', 'dependency', 'inversion'
        ];

        // Additional comprehensive word categories
        const additionalWords = [
            // Common verbs (action words)
            'accept', 'access', 'account', 'achieve', 'acquire', 'action', 'active', 'actual', 'address', 'admit', 'adopt', 'advance', 'advice', 'advise', 'affect', 'afford', 'agree', 'allow', 'almost', 'alone', 'along', 'already', 'although', 'always', 'among', 'amount', 'analyse', 'analyze', 'ancient', 'animal', 'annual', 'another', 'answer', 'anyone', 'appear', 'apply', 'approach', 'appropriate', 'argue', 'around', 'arrive', 'article', 'artist', 'assume', 'attack', 'attempt', 'attend', 'attention', 'attitude', 'attract', 'available', 'avoid', 'aware', 'become', 'before', 'begin', 'behalf', 'behave', 'behind', 'believe', 'benefit', 'better', 'between', 'beyond', 'billion', 'black', 'blood', 'board', 'break', 'bring', 'brother', 'budget', 'build', 'business', 'camera', 'cancer', 'capital', 'career', 'carry', 'catch', 'cause', 'centre', 'century', 'certain', 'certainly', 'chair', 'chairman', 'challenge', 'chance', 'change', 'character', 'charge', 'check', 'child', 'choice', 'choose', 'church', 'citizen', 'civil', 'claim', 'class', 'clear', 'clearly', 'close', 'coach', 'collection', 'college', 'colour', 'come', 'comment', 'commercial', 'commission', 'commit', 'committee', 'common', 'community', 'company', 'compare', 'complete', 'computer', 'concern', 'condition', 'conference', 'congress', 'consider', 'consumer', 'contain', 'continue', 'control', 'conversation', 'could', 'council', 'count', 'country', 'couple', 'course', 'court', 'cover', 'create', 'crime', 'cultural', 'culture', 'current', 'customer', 'daughter', 'death', 'debate', 'decade', 'decide', 'decision', 'defence', 'degree', 'democratic', 'describe', 'design', 'despite', 'detail', 'determine', 'develop', 'development', 'difference', 'different', 'difficult', 'dinner', 'direction', 'director', 'discover', 'discuss', 'discussion', 'disease', 'doctor', 'document', 'drive', 'during', 'early', 'economic', 'economy', 'education', 'effect', 'effective', 'effort', 'eight', 'either', 'election', 'electric', 'electronic', 'element', 'employee', 'energy', 'enjoy', 'enough', 'ensure', 'enter', 'entire', 'environment', 'environmental', 'especially', 'establish', 'estate', 'estimate', 'evening', 'event', 'eventually', 'every', 'everybody', 'everyone', 'everything', 'evidence', 'exactly', 'examine', 'example', 'excellent', 'except', 'exchange', 'executive', 'exercise', 'exist', 'existing', 'expect', 'experience', 'expert', 'explain', 'express', 'extra', 'extremely', 'factor', 'factory', 'family', 'famous', 'father', 'feature', 'federal', 'feeling', 'field', 'fight', 'figure', 'final', 'finally', 'finance', 'financial', 'finger', 'finish', 'first', 'focus', 'follow', 'following', 'force', 'foreign', 'forget', 'former', 'forward', 'foundation', 'frame', 'freedom', 'friend', 'front', 'full', 'fully', 'function', 'fund', 'funny', 'future', 'garden', 'general', 'generally', 'generation', 'given', 'glass', 'global', 'government', 'great', 'green', 'ground', 'group', 'growth', 'guess', 'guest', 'guide', 'hair', 'half', 'hand', 'handle', 'hang', 'happen', 'happy', 'hard', 'head', 'health', 'hear', 'heart', 'heat', 'heavy', 'help', 'high', 'himself', 'history', 'hold', 'holiday', 'home', 'hope', 'horse', 'hospital', 'hotel', 'hour', 'house', 'however', 'huge', 'human', 'hundred', 'husband', 'idea', 'identify', 'image', 'imagine', 'impact', 'important', 'improve', 'include', 'including', 'increase', 'indeed', 'independent', 'index', 'indicate', 'individual', 'industrial', 'industry', 'information', 'inside', 'instead', 'institution', 'interest', 'interesting', 'international', 'interview', 'investment', 'involve', 'issue', 'itself', 'join', 'jump', 'just', 'keep', 'kill', 'kind', 'kitchen', 'know', 'knowledge', 'labour', 'lack', 'lady', 'land', 'language', 'large', 'last', 'late', 'later', 'laugh', 'launch', 'lawyer', 'lead', 'leader', 'leadership', 'leading', 'learn', 'least', 'leave', 'left', 'legal', 'less', 'letter', 'level', 'library', 'life', 'light', 'like', 'likely', 'limit', 'line', 'link', 'list', 'listen', 'little', 'live', 'living', 'local', 'location', 'long', 'look', 'lose', 'loss', 'love', 'machine', 'magazine', 'main', 'maintain', 'major', 'make', 'making', 'management', 'manager', 'manner', 'many', 'market', 'marketing', 'marriage', 'material', 'matter', 'maybe', 'mean', 'meaning', 'means', 'meanwhile', 'measure', 'media', 'medical', 'meet', 'meeting', 'member', 'memory', 'mention', 'message', 'method', 'middle', 'might', 'military', 'million', 'mind', 'minister', 'minor', 'minute', 'miss', 'mission', 'mistake', 'model', 'modern', 'moment', 'money', 'month', 'morning', 'mother', 'motion', 'move', 'movement', 'movie', 'much', 'music', 'must', 'myself', 'name', 'nation', 'national', 'natural', 'nature', 'near', 'nearly', 'necessary', 'need', 'network', 'never', 'news', 'newspaper', 'next', 'nice', 'night', 'none', 'normal', 'north', 'note', 'nothing', 'notice', 'number', 'object', 'objective', 'obtain', 'obvious', 'occasion', 'occur', 'offer', 'office', 'officer', 'official', 'often', 'once', 'only', 'onto', 'open', 'operate', 'operation', 'opportunity', 'option', 'order', 'organisation', 'organize', 'original', 'other', 'others', 'otherwise', 'ought', 'outside', 'over', 'overall', 'owner', 'page', 'paint', 'paper', 'parent', 'part', 'particular', 'particularly', 'partner', 'party', 'pass', 'past', 'patient', 'pattern', 'payment', 'peace', 'people', 'perform', 'performance', 'perhaps', 'period', 'person', 'personal', 'phone', 'photograph', 'physical', 'pick', 'picture', 'piece', 'place', 'plan', 'planning', 'plant', 'play', 'player', 'please', 'pleasure', 'plus', 'point', 'police', 'policy', 'political', 'politics', 'poor', 'popular', 'population', 'position', 'positive', 'possible', 'post', 'potential', 'pound', 'power', 'practice', 'prepare', 'present', 'president', 'pressure', 'pretty', 'prevent', 'previous', 'price', 'primary', 'prime', 'principle', 'priority', 'private', 'probably', 'problem', 'procedure', 'process', 'produce', 'product', 'production', 'professional', 'professor', 'program', 'programme', 'project', 'property', 'propose', 'protect', 'protection', 'prove', 'provide', 'public', 'publication', 'publish', 'pull', 'purpose', 'push', 'quality', 'question', 'quick', 'quickly', 'quite', 'race', 'radio', 'raise', 'range', 'rate', 'rather', 'reach', 'read', 'ready', 'real', 'realise', 'reality', 'realize', 'really', 'reason', 'receive', 'recent', 'recently', 'recognise', 'recognize', 'recommend', 'record', 'recover', 'reduce', 'refer', 'reference', 'reflect', 'reform', 'regard', 'region', 'regional', 'regular', 'regulation', 'reject', 'relate', 'relation', 'relationship', 'relative', 'release', 'relevant', 'religion', 'religious', 'remain', 'remember', 'remove', 'repeat', 'replace', 'reply', 'report', 'represent', 'representative', 'reputation', 'request', 'require', 'requirement', 'research', 'resource', 'respond', 'response', 'responsibility', 'responsible', 'rest', 'result', 'return', 'reveal', 'revenue', 'review', 'revolution', 'rich', 'right', 'rise', 'risk', 'river', 'road', 'rock', 'role', 'roll', 'room', 'round', 'rule', 'safe', 'safety', 'sale', 'same', 'save', 'scale', 'scene', 'scheme', 'school', 'science', 'scientific', 'score', 'screen', 'search', 'season', 'seat', 'second', 'secondary', 'section', 'sector', 'secure', 'security', 'seek', 'seem', 'sell', 'send', 'senior', 'sense', 'series', 'serious', 'seriously', 'serve', 'service', 'session', 'seven', 'several', 'shall', 'share', 'sheet', 'ship', 'shirt', 'shoot', 'shop', 'short', 'shot', 'should', 'shoulder', 'show', 'side', 'sign', 'significant', 'significantly', 'similar', 'simple', 'simply', 'since', 'sing', 'single', 'sister', 'site', 'situation', 'size', 'skill', 'skin', 'small', 'smile', 'social', 'society', 'soft', 'software', 'soil', 'soldier', 'solid', 'solution', 'solve', 'somebody', 'someone', 'something', 'sometimes', 'somewhat', 'somewhere', 'song', 'soon', 'sort', 'sound', 'source', 'south', 'southern', 'space', 'speak', 'special', 'specialist', 'specific', 'specifically', 'speech', 'speed', 'spend', 'spin', 'spirit', 'spite', 'split', 'sport', 'spot', 'spread', 'spring', 'staff', 'stage', 'stand', 'standard', 'start', 'state', 'statement', 'station', 'status', 'stay', 'step', 'stick', 'still', 'stock', 'stop', 'storage', 'store', 'story', 'straight', 'strategy', 'street', 'strength', 'strike', 'string', 'strip', 'strong', 'strongly', 'structure', 'struggle', 'stuck', 'student', 'studio', 'study', 'stuff', 'stupid', 'style', 'subject', 'success', 'successful', 'successfully', 'such', 'sudden', 'suddenly', 'suffer', 'sufficient', 'suggest', 'suggestion', 'suitable', 'summer', 'supply', 'support', 'suppose', 'sure', 'surely', 'surface', 'surprise', 'surround', 'survey', 'survive', 'switch', 'system', 'table', 'take', 'taking', 'talk', 'target', 'task', 'teach', 'teacher', 'teaching', 'team', 'technical', 'technique', 'technology', 'telephone', 'television', 'tell', 'temperature', 'template', 'tend', 'term', 'terms', 'terrible', 'test', 'text', 'than', 'thank', 'thanks', 'that', 'theatre', 'their', 'them', 'theme', 'themselves', 'then', 'theory', 'there', 'therefore', 'these', 'they', 'thick', 'thin', 'thing', 'think', 'thinking', 'third', 'this', 'thorough', 'thoroughly', 'those', 'though', 'thought', 'thousand', 'threat', 'threaten', 'three', 'through', 'throughout', 'throw', 'thus', 'ticket', 'tight', 'till', 'time', 'tiny', 'title', 'today', 'together', 'tomorrow', 'tone', 'tonight', 'tool', 'tooth', 'topic', 'total', 'totally', 'touch', 'tough', 'tour', 'towards', 'town', 'track', 'trade', 'tradition', 'traditional', 'traffic', 'train', 'training', 'transfer', 'transform', 'transport', 'travel', 'treat', 'treatment', 'tree', 'trial', 'trip', 'trouble', 'truck', 'true', 'truly', 'trust', 'truth', 'turn', 'twelve', 'twenty', 'twice', 'type', 'typical', 'unable', 'under', 'understand', 'understanding', 'unfortunately', 'union', 'unique', 'unit', 'united', 'university', 'unless', 'unlike', 'unlikely', 'until', 'unusual', 'upon', 'upper', 'urban', 'used', 'useful', 'user', 'using', 'usual', 'usually', 'value', 'variation', 'variety', 'various', 'vast', 'vehicle', 'version', 'versus', 'very', 'victim', 'video', 'view', 'village', 'violence', 'violent', 'virtual', 'visit', 'visual', 'voice', 'volume', 'wait', 'wake', 'walk', 'wall', 'want', 'warm', 'warn', 'warning', 'wash', 'watch', 'water', 'wave', 'wear', 'weather', 'website', 'wedding', 'week', 'weekend', 'weekly', 'weight', 'welcome', 'well', 'west', 'western', 'what', 'whatever', 'wheel', 'when', 'whenever', 'where', 'whereas', 'whereby', 'wherever', 'whether', 'which', 'while', 'whilst', 'white', 'whole', 'whom', 'whose', 'wide', 'widely', 'wife', 'wild', 'will', 'willing', 'wind', 'window', 'wine', 'wing', 'winner', 'winning', 'winter', 'wire', 'wise', 'wish', 'with', 'within', 'without', 'woman', 'wonder', 'wonderful', 'wood', 'wooden', 'word', 'work', 'worker', 'working', 'workplace', 'world', 'worldwide', 'worry', 'worse', 'worst', 'worth', 'would', 'write', 'writer', 'writing', 'written', 'wrong', 'yard', 'yeah', 'year', 'yellow', 'yesterday', 'young', 'your', 'yourself', 'youth',

            // Technology and computing terms
            'algorithm', 'application', 'architecture', 'artificial', 'authentication', 'authorization', 'automation', 'backend', 'bandwidth', 'binary', 'blockchain', 'browser', 'bytecode', 'cache', 'certificate', 'client', 'cloud', 'cluster', 'code', 'coding', 'compilation', 'compiler', 'component', 'compression', 'computer', 'computing', 'configuration', 'connection', 'container', 'content', 'cookie', 'cryptography', 'cybersecurity', 'data', 'database', 'debugging', 'deployment', 'development', 'device', 'digital', 'directory', 'distribution', 'documentation', 'domain', 'download', 'driver', 'ecosystem', 'encryption', 'endpoint', 'engine', 'enterprise', 'environment', 'error', 'ethernet', 'event', 'exception', 'execution', 'extension', 'file', 'filesystem', 'filter', 'firewall', 'firmware', 'folder', 'framework', 'frontend', 'function', 'gateway', 'generation', 'graphics', 'hardware', 'hosting', 'hypertext', 'implementation', 'infrastructure', 'initialization', 'input', 'installation', 'instance', 'integration', 'interface', 'internet', 'interpreter', 'iteration', 'kernel', 'keyboard', 'language', 'library', 'license', 'lifecycle', 'link', 'linux', 'load', 'localhost', 'logic', 'machine', 'maintenance', 'management', 'memory', 'message', 'metadata', 'method', 'middleware', 'migration', 'mobile', 'model', 'module', 'monitor', 'mouse', 'multimedia', 'network', 'node', 'notification', 'object', 'operating', 'optimization', 'output', 'package', 'parameter', 'password', 'patch', 'path', 'pattern', 'performance', 'permission', 'platform', 'plugin', 'pointer', 'policy', 'port', 'process', 'processing', 'processor', 'program', 'programming', 'project', 'protocol', 'provider', 'proxy', 'query', 'queue', 'random', 'range', 'record', 'recovery', 'reference', 'registry', 'release', 'remote', 'repository', 'request', 'resource', 'response', 'result', 'router', 'routing', 'runtime', 'scalability', 'scaling', 'schema', 'script', 'scripting', 'search', 'security', 'server', 'service', 'session', 'setting', 'setup', 'shell', 'simulation', 'software', 'solution', 'source', 'specification', 'stack', 'standard', 'statement', 'storage', 'stream', 'string', 'structure', 'style', 'stylesheet', 'support', 'syntax', 'system', 'table', 'tag', 'target', 'task', 'technology', 'template', 'terminal', 'test', 'testing', 'text', 'thread', 'threading', 'timeout', 'timestamp', 'token', 'tool', 'toolkit', 'transaction', 'transfer', 'transformation', 'transmission', 'transport', 'tree', 'trigger', 'troubleshooting', 'tunnel', 'type', 'unicode', 'update', 'upgrade', 'upload', 'user', 'utility', 'validation', 'value', 'variable', 'vector', 'version', 'video', 'virtual', 'visualization', 'volume', 'web', 'website', 'widget', 'window', 'wireless', 'workflow', 'workspace', 'xml', 'yaml', 'zip',

            // Modern tech buzzwords and concepts
            'agile', 'analytics', 'api', 'artificial', 'augmented', 'automation', 'blockchain', 'chatbot', 'cloud', 'cognitive', 'collaboration', 'containerization', 'cryptocurrency', 'cybersecurity', 'dashboard', 'data', 'decentralized', 'deep', 'digital', 'distributed', 'edge', 'elastic', 'embedded', 'emerging', 'enterprise', 'ethereum', 'fintech', 'fullstack', 'gamification', 'hybrid', 'immersive', 'innovation', 'integration', 'intelligence', 'interactive', 'internet', 'iot', 'kubernetes', 'machine', 'microservices', 'mobile', 'modern', 'modular', 'neural', 'optimization', 'orchestration', 'personalization', 'platform', 'predictive', 'progressive', 'quantum', 'realtime', 'recommendation', 'responsive', 'scalable', 'serverless', 'smart', 'streaming', 'sustainable', 'synchronization', 'transformation', 'virtual', 'visualization', 'wearable'
        ];

        // Add context-specific words based on what's already in the text
        const contextWords = this.extractContextWords(context);

        return [...new Set([...commonWords, ...additionalWords, ...contextWords])];
    }

    extractContextWords(text) {
        // Extract words that appear in the current document
        const words = text.match(/\b[a-zA-Z][a-zA-Z0-9_]{2,}\b/g) || [];
        const uniqueWords = [...new Set(words.map(w => w.toLowerCase()))];
        return uniqueWords.filter(word => word.length >= 3);
    }

    getWordScore(word, context) {
        // Simple scoring based on word frequency in context and common usage
        const lowerWord = word.toLowerCase();
        const contextCount = (context.toLowerCase().match(new RegExp(`\\b${lowerWord}\\b`, 'g')) || []).length;

        // Boost score for programming terms if context seems technical
        const isTechnical = /\b(function|class|var|let|const|if|else|for|while|return|import|export)\b/i.test(context);
        const programmingTerms = ['function', 'class', 'method', 'variable', 'array', 'object', 'string', 'number', 'boolean', 'return', 'import', 'export', 'const', 'let', 'var'];

        let score = contextCount * 10; // Boost words already used in context

        if (isTechnical && programmingTerms.includes(lowerWord)) {
            score += 5;
        }

        // Boost common words
        const commonWords = ['the', 'and', 'for', 'function', 'return', 'class', 'method', 'variable', 'string', 'number', 'array', 'object'];
        if (commonWords.includes(lowerWord)) {
            score += 3;
        }

        return score;
    }

    preserveCapitalization(original, suggestion) {
        if (original.length === 0) return suggestion;

        // If original is all uppercase, make suggestion uppercase
        if (original === original.toUpperCase()) {
            return suggestion.toUpperCase();
        }

        // If original starts with uppercase, capitalize suggestion
        if (original[0] === original[0].toUpperCase()) {
            return suggestion.charAt(0).toUpperCase() + suggestion.slice(1).toLowerCase();
        }

        // Otherwise, keep suggestion lowercase
        return suggestion.toLowerCase();
    }

    isValidMathExpression(expr) {
        // Check if expression contains at least one number and one operator
        const hasNumber = /\d/.test(expr);
        const hasOperator = /[+\-*/×÷−]/.test(expr);
        const hasValidChars = /^[0-9+\-*/()×÷−.\s]+$/.test(expr);

        // Must have numbers, operators, and only valid characters
        // Minimum length to avoid single numbers
        return hasNumber && hasOperator && hasValidChars && expr.length >= 3;
    }

    evaluateExpression(expr) {
        try {
            // First, replace mobile/unicode math symbols with standard operators
            let cleanExpr = expr
                .replace(/×/g, '*')        // Multiplication symbol (×) → *
                .replace(/÷/g, '/')        // Division symbol (÷) → /
                .replace(/−/g, '-')        // Minus sign (−) → -
                .replace(/\u2212/g, '-')   // Unicode minus → -
                .replace(/\u00D7/g, '*')   // Unicode multiplication → *
                .replace(/\u00F7/g, '/');  // Unicode division → /

            // Clean the expression - allow mathematical characters including spaces
            cleanExpr = cleanExpr.replace(/[^0-9+\-*/().\s^%]/g, '');

            // Replace ^ with ** for JavaScript power operator
            cleanExpr = cleanExpr.replace(/\^/g, '**');

            // Remove extra spaces but keep structure
            cleanExpr = cleanExpr.replace(/\s+/g, ' ').trim();

            // Basic validation
            if (!cleanExpr || cleanExpr === '') return null;

            // Enhanced safety checks for complex expressions
            if (cleanExpr.includes('**')) {
                const powerCount = (cleanExpr.match(/\*\*/g) || []).length;
                if (powerCount > 3) {
                    return null; // Too many power operations
                }
            }

            // Check for balanced parentheses
            const openParens = (cleanExpr.match(/\(/g) || []).length;
            const closeParens = (cleanExpr.match(/\)/g) || []).length;
            if (openParens !== closeParens) {
                return null; // Unbalanced parentheses
            }

            // Prevent division by zero patterns
            if (/\/\s*0(?!\d)/.test(cleanExpr)) {
                return 'Division by zero';
            }

            // Evaluate safely with timeout protection
            const result = Function('"use strict"; return (' + cleanExpr + ')')();

            // Check if result is a valid number
            if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
                // Prevent extremely large numbers
                if (Math.abs(result) > 1e15) {
                    return 'Too large';
                }
                // Format the result nicely
                return this.formatResult(result);
            }

            return null;
        } catch (error) {
            // Return null for any evaluation errors
            return null;
        }
    }

    formatResult(result) {
        // Round to reasonable decimal places
        if (result % 1 === 0) {
            return result.toString();
        } else {
            return parseFloat(result.toFixed(6)).toString();
        }
    }

    showGhost(suggestion, editor, cursorPos, type = 'math') {
        this.isActive = true;
        this.suggestionType = type;

        // Calculate position more accurately
        const rect = editor.getBoundingClientRect();
        const textBeforeCursor = editor.value.substring(0, cursorPos);
        const lines = textBeforeCursor.split('\n');
        const currentLineIndex = lines.length - 1;
        const currentLineText = lines[currentLineIndex];

        // Create a temporary element to measure text with exact same styling
        const measurer = document.createElement('div');
        const editorStyles = getComputedStyle(editor);
        measurer.style.cssText = `
            position: absolute;
            visibility: hidden;
            white-space: pre;
            font-family: ${editorStyles.fontFamily};
            font-size: ${editorStyles.fontSize};
            line-height: ${editorStyles.lineHeight};
            font-weight: ${editorStyles.fontWeight};
            letter-spacing: ${editorStyles.letterSpacing};
            padding: 0;
            margin: 0;
            border: 0;
        `;
        measurer.textContent = currentLineText;
        document.body.appendChild(measurer);

        const textWidth = measurer.offsetWidth;
        const lineHeight = parseFloat(editorStyles.lineHeight) || 24;

        document.body.removeChild(measurer);

        // Calculate scroll offset
        const scrollTop = editor.scrollTop;
        const scrollLeft = editor.scrollLeft;

        // Position the ghost with proper spacing
        const paddingLeft = parseInt(editorStyles.paddingLeft) || 0;
        const paddingTop = parseInt(editorStyles.paddingTop) || 0;

        // Add small offset to position ghost right after the cursor
        const left = rect.left + textWidth + paddingLeft - scrollLeft + 2;
        const top = rect.top + (currentLineIndex * lineHeight) + paddingTop - scrollTop;

        // Store the original suggestion without extra spacing
        this.currentSuggestion = suggestion;

        // Display the suggestion with appropriate styling based on type
        this.ghostElement.textContent = suggestion;
        this.ghostElement.style.left = left + 'px';
        this.ghostElement.style.top = top + 'px';
        this.ghostElement.style.display = 'block';

        // Different styling for different types
        if (type === 'word') {
            this.ghostElement.style.background = 'rgba(76, 175, 80, 0.1)';
            this.ghostElement.style.color = 'rgba(76, 175, 80, 0.7)';
        } else {
            this.ghostElement.style.background = 'rgba(0, 180, 216, 0.1)';
            this.ghostElement.style.color = 'rgba(255, 255, 255, 0.4)';
        }

        // Add a subtle animation
        this.ghostElement.style.opacity = '0';
        this.ghostElement.style.transform = 'translateX(-5px)';

        requestAnimationFrame(() => {
            this.ghostElement.style.opacity = '1';
            this.ghostElement.style.transform = 'translateX(0)';
        });
    }

    hideGhost() {
        this.ghostElement.style.display = 'none';
        this.isActive = false;
        this.currentSuggestion = null;
        this.suggestionType = null;
    }

    acceptSuggestion() {
        if (!this.isActive || !this.currentSuggestion) return;

        const editor = document.getElementById('editor');
        if (!editor) return;

        const cursorPos = editor.selectionStart;
        const text = editor.value;

        // Use the suggestion exactly as stored - no modifications
        const suggestionToInsert = this.currentSuggestion;

        const newText = text.substring(0, cursorPos) + suggestionToInsert + text.substring(cursorPos);
        editor.value = newText;

        // Move cursor to end of inserted text
        const newCursorPos = cursorPos + suggestionToInsert.length;
        editor.setSelectionRange(newCursorPos, newCursorPos);

        // Trigger input event to update word count, etc.
        editor.dispatchEvent(new Event('input', { bubbles: true }));

        this.hideGhost();
        editor.focus();
    }
}

// Create MathAutoComplete class that extends BaseAutoComplete
class MathAutoComplete extends BaseAutoComplete {
    constructor() {
        super();
    }
}

// Comprehensive AI Auto-Complete with both Math and Word completion
class AIAutoComplete extends BaseAutoComplete {
    constructor() {
        super();
        this.mathFunctions = {
            'sqrt': Math.sqrt,
            'sin': Math.sin,
            'cos': Math.cos,
            'tan': Math.tan,
            'log': Math.log10,
            'ln': Math.log,
            'abs': Math.abs,
            'round': Math.round,
            'floor': Math.floor,
            'ceil': Math.ceil,
            'pow': Math.pow
        };
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced function pattern - support complex expressions in function arguments
        const functionPattern = /(\w+)\s*\(\s*([^)]+)\s*\)\s*=\s*$/;
        const functionMatch = line.match(functionPattern);

        if (functionMatch) {
            const funcName = functionMatch[1].toLowerCase();
            let argument = functionMatch[2];

            // Convert mobile math symbols in function arguments too
            argument = argument
                .replace(/×/g, '*')
                .replace(/÷/g, '/')
                .replace(/−/g, '-')
                .replace(/\u2212/g, '-')
                .replace(/\u00D7/g, '*')
                .replace(/\u00F7/g, '/');

            if (this.mathFunctions[funcName]) {
                try {
                    let result;

                    if (funcName === 'pow') {
                        // Handle power function specially (needs two arguments)
                        const args = argument.split(',').map(x => x.trim());
                        if (args.length === 2) {
                            const arg1 = this.evaluateExpression(args[0]) || parseFloat(args[0]);
                            const arg2 = this.evaluateExpression(args[1]) || parseFloat(args[1]);

                            if (!isNaN(arg1) && !isNaN(arg2)) {
                                result = Math.pow(parseFloat(arg1), parseFloat(arg2));
                            }
                        }
                    } else {
                        // Handle single argument functions with complex expressions
                        // Try to evaluate as expression first, then as simple number
                        let argValue;
                        const complexResult = this.evaluateExpression(argument);

                        if (complexResult !== null && !isNaN(parseFloat(complexResult))) {
                            argValue = parseFloat(complexResult);
                        } else {
                            argValue = parseFloat(argument);
                        }

                        if (!isNaN(argValue)) {
                            result = this.mathFunctions[funcName](argValue);
                        }
                    }

                    if (result !== undefined && !isNaN(result) && isFinite(result)) {
                        // Prevent extremely large numbers
                        if (Math.abs(result) > 1e15) {
                            this.showGhost('Too large', editor, cursorPos, 'math');
                        } else {
                            this.showGhost(this.formatResult(result), editor, cursorPos, 'math');
                        }
                        return true;
                    }
                } catch (error) {
                    // Ignore errors and fall through
                }
            }
        }

        // Fall back to basic math
        return super.checkForMathExpression(line, editor, cursorPos);
    }
}

// Keep the original AdvancedMathAutoComplete for backward compatibility
class AdvancedMathAutoComplete extends AIAutoComplete {
    constructor() {
        super();
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced function pattern - support complex expressions in function arguments
        const functionPattern = /(\w+)\s*\(\s*([^)]+)\s*\)\s*=\s*$/;
        const functionMatch = line.match(functionPattern);

        if (functionMatch) {
            const funcName = functionMatch[1].toLowerCase();
            let argument = functionMatch[2];

            // Convert mobile math symbols in function arguments too
            argument = argument
                .replace(/×/g, '*')
                .replace(/÷/g, '/')
                .replace(/−/g, '-')
                .replace(/\u2212/g, '-')
                .replace(/\u00D7/g, '*')
                .replace(/\u00F7/g, '/');

            if (this.mathFunctions[funcName]) {
                try {
                    let result;

                    if (funcName === 'pow') {
                        // Handle power function specially (needs two arguments)
                        const args = argument.split(',').map(x => x.trim());
                        if (args.length === 2) {
                            const arg1 = this.evaluateExpression(args[0]) || parseFloat(args[0]);
                            const arg2 = this.evaluateExpression(args[1]) || parseFloat(args[1]);

                            if (!isNaN(arg1) && !isNaN(arg2)) {
                                result = Math.pow(parseFloat(arg1), parseFloat(arg2));
                            }
                        }
                    } else {
                        // Handle single argument functions with complex expressions
                        // Try to evaluate as expression first, then as simple number
                        let argValue;
                        const complexResult = this.evaluateExpression(argument);

                        if (complexResult !== null && !isNaN(parseFloat(complexResult))) {
                            argValue = parseFloat(complexResult);
                        } else {
                            argValue = parseFloat(argument);
                        }

                        if (!isNaN(argValue)) {
                            result = this.mathFunctions[funcName](argValue);
                        }
                    }

                    if (result !== undefined && !isNaN(result) && isFinite(result)) {
                        // Prevent extremely large numbers
                        if (Math.abs(result) > 1e15) {
                            this.showGhost('Too large', editor, cursorPos, 'math');
                        } else {
                            this.showGhost(this.formatResult(result), editor, cursorPos, 'math');
                        }
                        return true;
                    }
                } catch (error) {
                    // Ignore errors and fall through
                }
            }
        }

        // Fall back to basic math
        return super.checkForMathExpression(line, editor, cursorPos);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other scripts to initialize
    setTimeout(() => {
        window.aiAutoComplete = new AIAutoComplete();
        console.log('AI Auto-Complete initialized (Math + Word completion)');
    }, 100);
});

// Export for potential external use
window.BaseAutoComplete = BaseAutoComplete;
window.MathAutoComplete = MathAutoComplete;
window.AIAutoComplete = AIAutoComplete;
window.AdvancedMathAutoComplete = AdvancedMathAutoComplete;
