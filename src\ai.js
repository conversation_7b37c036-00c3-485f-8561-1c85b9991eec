'use strict';

/**
 * AI Auto-Complete System
 *
 * Features:
 * 1. Math Auto-Complete:
 *    - Standard: +, -, *, /
 *    - Mobile: ×, ÷, − (Unicode minus)
 *    - Functions: sqrt, sin, cos, tan, log, ln, abs, round, floor, ceil, pow
 *    - Examples: 5 × 3 = → shows "15", sqrt(25) = → shows "5"
 *
 * 2. Word Auto-Complete:
 *    - Common English words
 *    - Programming keywords
 *    - Context-aware suggestions
 *    - Smart capitalization
 *    - Examples: "hel" → suggests "hello", "func" → suggests "function"
 *
 * Usage:
 * - Math: Type expression ending with =
 * - Words: Type partial word (3+ characters)
 * - Accept: Click ghost, press Tab, or press Enter
 * - Dismiss: Press Esc or click elsewhere
 */

class BaseAutoComplete {
    constructor() {
        this.ghostElement = null;
        this.currentSuggestion = null;
        this.isActive = false;
        this.suggestionType = null; // 'math' or 'word'
        this.init();
    }

    init() {
        this.createGhostElement();
        this.bindEvents();
    }

    createGhostElement() {
        this.ghostElement = document.createElement('div');
        this.ghostElement.className = 'math-ghost-suggestion';
        this.ghostElement.style.cssText = `
            position: absolute;
            color: rgba(255, 255, 255, 0.4);
            pointer-events: auto;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            white-space: pre;
            z-index: 1000;
            display: none;
            user-select: none;
            background: rgba(0, 180, 216, 0.1);
            border-radius: 3px;
            padding: 0 2px;
            transition: all 0.2s ease;
        `;
        
        this.ghostElement.addEventListener('click', () => {
            this.acceptSuggestion();
        });

        this.ghostElement.addEventListener('mouseenter', () => {
            if (this.suggestionType === 'word') {
                this.ghostElement.style.background = 'rgba(76, 175, 80, 0.2)';
            } else {
                this.ghostElement.style.background = 'rgba(0, 180, 216, 0.2)';
            }
        });

        this.ghostElement.addEventListener('mouseleave', () => {
            if (this.suggestionType === 'word') {
                this.ghostElement.style.background = 'rgba(76, 175, 80, 0.1)';
            } else {
                this.ghostElement.style.background = 'rgba(0, 180, 216, 0.1)';
            }
        });

        document.body.appendChild(this.ghostElement);
    }

    bindEvents() {
        const editor = document.getElementById('editor');
        if (!editor) return;

        editor.addEventListener('input', (e) => {
            this.handleInput(e);
        });

        editor.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        }, true); // Use capture phase to handle before other listeners

        editor.addEventListener('scroll', () => {
            this.hideGhost();
        });

        // Hide ghost when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (e.target !== this.ghostElement && e.target !== editor) {
                this.hideGhost();
            }
        });
    }

    handleInput(event) {
        const editor = event.target;
        const text = editor.value;
        const cursorPos = editor.selectionStart;

        // Get current line
        const lines = text.substring(0, cursorPos).split('\n');
        const currentLine = lines[lines.length - 1];

        // Try math auto-complete first, then word auto-complete
        if (!this.checkForMathExpression(currentLine, editor, cursorPos)) {
            this.checkForWordCompletion(currentLine, editor, cursorPos, text);
        }
    }

    handleKeydown(event) {
        if (this.isActive) {
            if (event.key === 'Tab' || event.key === 'ArrowRight') {
                event.preventDefault();
                event.stopPropagation();
                this.acceptSuggestion();
            } else if (event.key === 'Enter') {
                event.preventDefault();
                event.stopPropagation();
                this.acceptSuggestion();
                // Add a new line after accepting the suggestion
                const editor = document.getElementById('editor');
                if (editor) {
                    const cursorPos = editor.selectionStart;
                    const text = editor.value;
                    const newText = text.substring(0, cursorPos) + '\n' + text.substring(cursorPos);
                    editor.value = newText;
                    editor.setSelectionRange(cursorPos + 1, cursorPos + 1);
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }
            } else if (event.key === 'Escape') {
                this.hideGhost();
            }
        }
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced math expression patterns - support complex expressions
        const patterns = [
            // Complex expressions with equals: 1+2+3=, 5*2-3+1=, (2+3)*4-1=
            /^(.+?)\s*=\s*$/,
            // Preview mode - show result for expressions without equals
            // Support multiple operators: 1+2+3, 5*2-3, (2+3)*4
            /^([0-9+\-*/()×÷−.\s]+)$/
        ];

        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                let expression;
                if (pattern.source.includes('=')) {
                    // Has equals sign - extract expression before =
                    expression = match[1].trim();
                } else {
                    // No equals sign - use full match for preview
                    expression = match[0].trim();
                }

                // Only process if expression contains operators and numbers
                if (this.isValidMathExpression(expression)) {
                    const result = this.evaluateExpression(expression);

                    if (result !== null) {
                        // Check if line ends with = to determine suggestion format
                        const suggestion = line.includes('=') ? result : `= ${result}`;
                        this.showGhost(suggestion, editor, cursorPos, 'math');
                        return true;
                    }
                }
            }
        }

        return false;
    }

    checkForWordCompletion(line, editor, cursorPos, fullText) {
        // Get the word being typed at cursor position
        const wordMatch = this.getCurrentWord(line, cursorPos, fullText);
        if (!wordMatch) {
            this.hideGhost();
            return false;
        }

        const { word } = wordMatch;

        // Only suggest for words with 3+ characters
        if (word.length < 3) {
            this.hideGhost();
            return false;
        }

        // Find best word suggestion
        const suggestion = this.findBestWordSuggestion(word, fullText);
        if (suggestion && suggestion.toLowerCase() !== word.toLowerCase()) {
            // Show only the remaining part of the word
            const completion = suggestion.substring(word.length);
            this.showGhost(completion, editor, cursorPos, 'word');
            return true;
        }

        this.hideGhost();
        return false;
    }

    getCurrentWord(line, cursorPos, fullText) {
        // Find word boundaries around cursor
        const textBeforeCursor = fullText.substring(0, cursorPos);
        const lines = textBeforeCursor.split('\n');
        const currentLineText = lines[lines.length - 1];

        // Find the start of the current word by looking backwards from cursor
        let wordStart = currentLineText.length;
        for (let i = currentLineText.length - 1; i >= 0; i--) {
            const char = currentLineText[i];
            if (/[a-zA-Z0-9_]/.test(char)) {
                wordStart = i;
            } else {
                wordStart = i + 1; // Start after the non-word character
                break;
            }
        }

        // Extract the current word
        const word = currentLineText.substring(wordStart);

        // Only return valid words that start with a letter
        if (word && /^[a-zA-Z][a-zA-Z0-9_]*$/.test(word)) {
            return { word, startPos: wordStart };
        }

        return null;
    }

    findBestWordSuggestion(partialWord, context) {
        const lowerPartial = partialWord.toLowerCase();

        // Get word dictionary
        const dictionary = this.getWordDictionary(context);

        // Find exact prefix matches first
        const exactMatches = dictionary.filter(word =>
            word.toLowerCase().startsWith(lowerPartial) &&
            word.toLowerCase() !== lowerPartial
        );

        if (exactMatches.length > 0) {
            // Sort by length (prefer shorter completions) and frequency
            exactMatches.sort((a, b) => {
                const aScore = this.getWordScore(a, context);
                const bScore = this.getWordScore(b, context);
                if (aScore !== bScore) return bScore - aScore;
                return a.length - b.length;
            });

            // Preserve original capitalization pattern
            return this.preserveCapitalization(partialWord, exactMatches[0]);
        }

        return null;
    }

    getWordDictionary(context) {
        // Common English words and programming terms
        const commonWords = [
            // Common English words
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use',
            'about', 'after', 'again', 'before', 'being', 'below', 'between', 'both', 'during', 'each', 'few', 'from', 'further', 'here', 'how', 'into', 'more', 'most', 'other', 'over', 'same', 'some', 'such', 'than', 'that', 'their', 'them', 'these', 'they', 'this', 'those', 'through', 'time', 'very', 'what', 'when', 'where', 'which', 'while', 'with', 'would', 'your',
            'hello', 'world', 'example', 'function', 'variable', 'string', 'number', 'boolean', 'array', 'object', 'method', 'class', 'interface', 'implementation', 'algorithm', 'structure', 'database', 'application', 'development', 'programming', 'computer', 'software', 'hardware', 'network', 'internet', 'website', 'server', 'client', 'framework', 'library',

            // Programming keywords and terms
            'function', 'return', 'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'break', 'continue', 'try', 'catch', 'finally', 'throw', 'class', 'interface', 'extends', 'implements', 'import', 'export', 'const', 'let', 'var', 'async', 'await', 'promise', 'callback', 'event', 'listener',
            'javascript', 'python', 'java', 'cpp', 'html', 'css', 'react', 'angular', 'vue', 'node', 'express', 'mongodb', 'mysql', 'postgresql', 'redis', 'docker', 'kubernetes', 'git', 'github', 'gitlab', 'bitbucket',
            'component', 'service', 'controller', 'model', 'view', 'template', 'directive', 'pipe', 'filter', 'middleware', 'router', 'route', 'endpoint', 'api', 'rest', 'graphql', 'json', 'xml', 'yaml', 'configuration',

            // Common verbs and actions
            'create', 'read', 'update', 'delete', 'insert', 'select', 'modify', 'remove', 'add', 'edit', 'save', 'load', 'fetch', 'send', 'receive', 'process', 'handle', 'manage', 'execute', 'run', 'start', 'stop', 'pause', 'resume', 'cancel', 'submit', 'validate', 'verify', 'authenticate', 'authorize',

            // Technical terms
            'database', 'table', 'column', 'row', 'index', 'query', 'transaction', 'connection', 'session', 'cache', 'memory', 'storage', 'file', 'directory', 'folder', 'path', 'url', 'uri', 'protocol', 'http', 'https', 'tcp', 'udp', 'ip', 'dns', 'ssl', 'tls', 'certificate', 'encryption', 'decryption', 'hash', 'algorithm', 'security', 'authentication', 'authorization', 'permission', 'access', 'control'
        ];

        // Add context-specific words based on what's already in the text
        const contextWords = this.extractContextWords(context);

        return [...new Set([...commonWords, ...contextWords])];
    }

    extractContextWords(text) {
        // Extract words that appear in the current document
        const words = text.match(/\b[a-zA-Z][a-zA-Z0-9_]{2,}\b/g) || [];
        const uniqueWords = [...new Set(words.map(w => w.toLowerCase()))];
        return uniqueWords.filter(word => word.length >= 3);
    }

    getWordScore(word, context) {
        // Simple scoring based on word frequency in context and common usage
        const lowerWord = word.toLowerCase();
        const contextCount = (context.toLowerCase().match(new RegExp(`\\b${lowerWord}\\b`, 'g')) || []).length;

        // Boost score for programming terms if context seems technical
        const isTechnical = /\b(function|class|var|let|const|if|else|for|while|return|import|export)\b/i.test(context);
        const programmingTerms = ['function', 'class', 'method', 'variable', 'array', 'object', 'string', 'number', 'boolean', 'return', 'import', 'export', 'const', 'let', 'var'];

        let score = contextCount * 10; // Boost words already used in context

        if (isTechnical && programmingTerms.includes(lowerWord)) {
            score += 5;
        }

        // Boost common words
        const commonWords = ['the', 'and', 'for', 'function', 'return', 'class', 'method', 'variable', 'string', 'number', 'array', 'object'];
        if (commonWords.includes(lowerWord)) {
            score += 3;
        }

        return score;
    }

    preserveCapitalization(original, suggestion) {
        if (original.length === 0) return suggestion;

        // If original is all uppercase, make suggestion uppercase
        if (original === original.toUpperCase()) {
            return suggestion.toUpperCase();
        }

        // If original starts with uppercase, capitalize suggestion
        if (original[0] === original[0].toUpperCase()) {
            return suggestion.charAt(0).toUpperCase() + suggestion.slice(1).toLowerCase();
        }

        // Otherwise, keep suggestion lowercase
        return suggestion.toLowerCase();
    }

    isValidMathExpression(expr) {
        // Check if expression contains at least one number and one operator
        const hasNumber = /\d/.test(expr);
        const hasOperator = /[+\-*/×÷−]/.test(expr);
        const hasValidChars = /^[0-9+\-*/()×÷−.\s]+$/.test(expr);

        // Must have numbers, operators, and only valid characters
        // Minimum length to avoid single numbers
        return hasNumber && hasOperator && hasValidChars && expr.length >= 3;
    }

    evaluateExpression(expr) {
        try {
            // First, replace mobile/unicode math symbols with standard operators
            let cleanExpr = expr
                .replace(/×/g, '*')        // Multiplication symbol (×) → *
                .replace(/÷/g, '/')        // Division symbol (÷) → /
                .replace(/−/g, '-')        // Minus sign (−) → -
                .replace(/\u2212/g, '-')   // Unicode minus → -
                .replace(/\u00D7/g, '*')   // Unicode multiplication → *
                .replace(/\u00F7/g, '/');  // Unicode division → /

            // Clean the expression - allow mathematical characters including spaces
            cleanExpr = cleanExpr.replace(/[^0-9+\-*/().\s^%]/g, '');

            // Replace ^ with ** for JavaScript power operator
            cleanExpr = cleanExpr.replace(/\^/g, '**');

            // Remove extra spaces but keep structure
            cleanExpr = cleanExpr.replace(/\s+/g, ' ').trim();

            // Basic validation
            if (!cleanExpr || cleanExpr === '') return null;

            // Enhanced safety checks for complex expressions
            if (cleanExpr.includes('**')) {
                const powerCount = (cleanExpr.match(/\*\*/g) || []).length;
                if (powerCount > 3) {
                    return null; // Too many power operations
                }
            }

            // Check for balanced parentheses
            const openParens = (cleanExpr.match(/\(/g) || []).length;
            const closeParens = (cleanExpr.match(/\)/g) || []).length;
            if (openParens !== closeParens) {
                return null; // Unbalanced parentheses
            }

            // Prevent division by zero patterns
            if (/\/\s*0(?!\d)/.test(cleanExpr)) {
                return 'Division by zero';
            }

            // Evaluate safely with timeout protection
            const result = Function('"use strict"; return (' + cleanExpr + ')')();

            // Check if result is a valid number
            if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
                // Prevent extremely large numbers
                if (Math.abs(result) > 1e15) {
                    return 'Too large';
                }
                // Format the result nicely
                return this.formatResult(result);
            }

            return null;
        } catch (error) {
            // Return null for any evaluation errors
            return null;
        }
    }

    formatResult(result) {
        // Round to reasonable decimal places
        if (result % 1 === 0) {
            return result.toString();
        } else {
            return parseFloat(result.toFixed(6)).toString();
        }
    }

    showGhost(suggestion, editor, cursorPos, type = 'math') {
        this.isActive = true;
        this.suggestionType = type;

        // Calculate position more accurately
        const rect = editor.getBoundingClientRect();
        const textBeforeCursor = editor.value.substring(0, cursorPos);
        const lines = textBeforeCursor.split('\n');
        const currentLineIndex = lines.length - 1;
        const currentLineText = lines[currentLineIndex];

        // Create a temporary element to measure text with exact same styling
        const measurer = document.createElement('div');
        const editorStyles = getComputedStyle(editor);
        measurer.style.cssText = `
            position: absolute;
            visibility: hidden;
            white-space: pre;
            font-family: ${editorStyles.fontFamily};
            font-size: ${editorStyles.fontSize};
            line-height: ${editorStyles.lineHeight};
            font-weight: ${editorStyles.fontWeight};
            letter-spacing: ${editorStyles.letterSpacing};
            padding: 0;
            margin: 0;
            border: 0;
        `;
        measurer.textContent = currentLineText;
        document.body.appendChild(measurer);

        const textWidth = measurer.offsetWidth;
        const lineHeight = parseFloat(editorStyles.lineHeight) || 24;

        document.body.removeChild(measurer);

        // Calculate scroll offset
        const scrollTop = editor.scrollTop;
        const scrollLeft = editor.scrollLeft;

        // Position the ghost with proper spacing
        const paddingLeft = parseInt(editorStyles.paddingLeft) || 0;
        const paddingTop = parseInt(editorStyles.paddingTop) || 0;

        // Add small offset to position ghost right after the cursor
        const left = rect.left + textWidth + paddingLeft - scrollLeft + 2;
        const top = rect.top + (currentLineIndex * lineHeight) + paddingTop - scrollTop;

        // Store the original suggestion without extra spacing
        this.currentSuggestion = suggestion;

        // Display the suggestion with appropriate styling based on type
        this.ghostElement.textContent = suggestion;
        this.ghostElement.style.left = left + 'px';
        this.ghostElement.style.top = top + 'px';
        this.ghostElement.style.display = 'block';

        // Different styling for different types
        if (type === 'word') {
            this.ghostElement.style.background = 'rgba(76, 175, 80, 0.1)';
            this.ghostElement.style.color = 'rgba(76, 175, 80, 0.7)';
        } else {
            this.ghostElement.style.background = 'rgba(0, 180, 216, 0.1)';
            this.ghostElement.style.color = 'rgba(255, 255, 255, 0.4)';
        }

        // Add a subtle animation
        this.ghostElement.style.opacity = '0';
        this.ghostElement.style.transform = 'translateX(-5px)';

        requestAnimationFrame(() => {
            this.ghostElement.style.opacity = '1';
            this.ghostElement.style.transform = 'translateX(0)';
        });
    }

    hideGhost() {
        this.ghostElement.style.display = 'none';
        this.isActive = false;
        this.currentSuggestion = null;
        this.suggestionType = null;
    }

    acceptSuggestion() {
        if (!this.isActive || !this.currentSuggestion) return;

        const editor = document.getElementById('editor');
        if (!editor) return;

        const cursorPos = editor.selectionStart;
        const text = editor.value;

        // Use the suggestion exactly as stored - no modifications
        const suggestionToInsert = this.currentSuggestion;

        const newText = text.substring(0, cursorPos) + suggestionToInsert + text.substring(cursorPos);
        editor.value = newText;

        // Move cursor to end of inserted text
        const newCursorPos = cursorPos + suggestionToInsert.length;
        editor.setSelectionRange(newCursorPos, newCursorPos);

        // Trigger input event to update word count, etc.
        editor.dispatchEvent(new Event('input', { bubbles: true }));

        this.hideGhost();
        editor.focus();
    }
}

// Create MathAutoComplete class that extends BaseAutoComplete
class MathAutoComplete extends BaseAutoComplete {
    constructor() {
        super();
    }
}

// Comprehensive AI Auto-Complete with both Math and Word completion
class AIAutoComplete extends BaseAutoComplete {
    constructor() {
        super();
        this.mathFunctions = {
            'sqrt': Math.sqrt,
            'sin': Math.sin,
            'cos': Math.cos,
            'tan': Math.tan,
            'log': Math.log10,
            'ln': Math.log,
            'abs': Math.abs,
            'round': Math.round,
            'floor': Math.floor,
            'ceil': Math.ceil,
            'pow': Math.pow
        };
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced function pattern - support complex expressions in function arguments
        const functionPattern = /(\w+)\s*\(\s*([^)]+)\s*\)\s*=\s*$/;
        const functionMatch = line.match(functionPattern);

        if (functionMatch) {
            const funcName = functionMatch[1].toLowerCase();
            let argument = functionMatch[2];

            // Convert mobile math symbols in function arguments too
            argument = argument
                .replace(/×/g, '*')
                .replace(/÷/g, '/')
                .replace(/−/g, '-')
                .replace(/\u2212/g, '-')
                .replace(/\u00D7/g, '*')
                .replace(/\u00F7/g, '/');

            if (this.mathFunctions[funcName]) {
                try {
                    let result;

                    if (funcName === 'pow') {
                        // Handle power function specially (needs two arguments)
                        const args = argument.split(',').map(x => x.trim());
                        if (args.length === 2) {
                            const arg1 = this.evaluateExpression(args[0]) || parseFloat(args[0]);
                            const arg2 = this.evaluateExpression(args[1]) || parseFloat(args[1]);

                            if (!isNaN(arg1) && !isNaN(arg2)) {
                                result = Math.pow(parseFloat(arg1), parseFloat(arg2));
                            }
                        }
                    } else {
                        // Handle single argument functions with complex expressions
                        // Try to evaluate as expression first, then as simple number
                        let argValue;
                        const complexResult = this.evaluateExpression(argument);

                        if (complexResult !== null && !isNaN(parseFloat(complexResult))) {
                            argValue = parseFloat(complexResult);
                        } else {
                            argValue = parseFloat(argument);
                        }

                        if (!isNaN(argValue)) {
                            result = this.mathFunctions[funcName](argValue);
                        }
                    }

                    if (result !== undefined && !isNaN(result) && isFinite(result)) {
                        // Prevent extremely large numbers
                        if (Math.abs(result) > 1e15) {
                            this.showGhost('Too large', editor, cursorPos, 'math');
                        } else {
                            this.showGhost(this.formatResult(result), editor, cursorPos, 'math');
                        }
                        return true;
                    }
                } catch (error) {
                    // Ignore errors and fall through
                }
            }
        }

        // Fall back to basic math
        return super.checkForMathExpression(line, editor, cursorPos);
    }
}

// Keep the original AdvancedMathAutoComplete for backward compatibility
class AdvancedMathAutoComplete extends AIAutoComplete {
    constructor() {
        super();
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Enhanced function pattern - support complex expressions in function arguments
        const functionPattern = /(\w+)\s*\(\s*([^)]+)\s*\)\s*=\s*$/;
        const functionMatch = line.match(functionPattern);

        if (functionMatch) {
            const funcName = functionMatch[1].toLowerCase();
            let argument = functionMatch[2];

            // Convert mobile math symbols in function arguments too
            argument = argument
                .replace(/×/g, '*')
                .replace(/÷/g, '/')
                .replace(/−/g, '-')
                .replace(/\u2212/g, '-')
                .replace(/\u00D7/g, '*')
                .replace(/\u00F7/g, '/');

            if (this.mathFunctions[funcName]) {
                try {
                    let result;

                    if (funcName === 'pow') {
                        // Handle power function specially (needs two arguments)
                        const args = argument.split(',').map(x => x.trim());
                        if (args.length === 2) {
                            const arg1 = this.evaluateExpression(args[0]) || parseFloat(args[0]);
                            const arg2 = this.evaluateExpression(args[1]) || parseFloat(args[1]);

                            if (!isNaN(arg1) && !isNaN(arg2)) {
                                result = Math.pow(parseFloat(arg1), parseFloat(arg2));
                            }
                        }
                    } else {
                        // Handle single argument functions with complex expressions
                        // Try to evaluate as expression first, then as simple number
                        let argValue;
                        const complexResult = this.evaluateExpression(argument);

                        if (complexResult !== null && !isNaN(parseFloat(complexResult))) {
                            argValue = parseFloat(complexResult);
                        } else {
                            argValue = parseFloat(argument);
                        }

                        if (!isNaN(argValue)) {
                            result = this.mathFunctions[funcName](argValue);
                        }
                    }

                    if (result !== undefined && !isNaN(result) && isFinite(result)) {
                        // Prevent extremely large numbers
                        if (Math.abs(result) > 1e15) {
                            this.showGhost('Too large', editor, cursorPos, 'math');
                        } else {
                            this.showGhost(this.formatResult(result), editor, cursorPos, 'math');
                        }
                        return true;
                    }
                } catch (error) {
                    // Ignore errors and fall through
                }
            }
        }

        // Fall back to basic math
        return super.checkForMathExpression(line, editor, cursorPos);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other scripts to initialize
    setTimeout(() => {
        window.aiAutoComplete = new AIAutoComplete();
        console.log('AI Auto-Complete initialized (Math + Word completion)');
    }, 100);
});

// Export for potential external use
window.BaseAutoComplete = BaseAutoComplete;
window.MathAutoComplete = MathAutoComplete;
window.AIAutoComplete = AIAutoComplete;
